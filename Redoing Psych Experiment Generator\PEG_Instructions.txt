
You are a senior Python developer and experimental psychologist. Your task is to develop a simple and powerful experiment building platform that can deploy ready-made experiments in HTML format. An inspiration for this program is [[Psytoolkit]], another free experiment generator built in the C language that outputs fully self-contained HTML files as experiments. 

Given the complexity of this program, we will iterate development in simple steps. I will describe what the goal is, and which steps have been achieved. 



========

<step1>

Goal: Create a basic working interface

Overview: Imagine a  spreadsheet-like input structure with three columns, labelled 'Stimulus', 'Response', 'Latency'. The row numbers can be fixed at five for the display, but it should be possible to add new rows using a '+' icon underneath the last row.  Beneath that should be four buttons called 'Start', 'Deploy', 'Save', and 'Load'. Other than the 'Start' button, which will compile and run the task, the remaining buttons can be placeholders for now. 

Details: 
Under 'Stimulus', the user can enter any combination of characters, including spaces. This shows the text to display. Under 'Response', the user can enter any number of acceptable responses, separated by commas. Under 'Latency', the number of milliseconds that said stimulus will appear will be provided. Note that both 'Response' and 'Latency' options have a 'NA' input option. That is, when 'Response' is NA, the stimulus will progress based on the latency condition only. When the latency is NA, the stimulus will progress when there is a response recorded only. If the user enters NA for both Response and Latency, pressing the Start button will produce a warning message stating that at least one of those properties have to be non-NA. If all inputs are correct (examples provided below), than pressing Start will display a 'Compiling' message, then run the task. All stimuli can be presented in a white Arial times 14 font against a dark grey background on screen center. 

Examples:

(A simple dot probe task)

Stimulus | Response | Latency
-------------------------------------------
+ | NA | 500 
. |space, n | 500
  | NA | 2000



(An appetitive conditioning task)

Stimulus | Response | Latency
-------------------------------------------
+ | NA | 100
eating | NA | 500
 | NA | 200
happy | NA | 500
do you feel hungry? | y, n | NA




</step1>